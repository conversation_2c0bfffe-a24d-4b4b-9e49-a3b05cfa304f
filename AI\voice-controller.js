/**
 * 语音控制器 - 实现语音唤醒和交互功能
 * 支持唤醒词检测、命令识别和语音应答
 */
class VoiceController {
    constructor() {
        // 语音识别相关
        this.recognition = null;
        this.isSupported = false;
        this.isListening = false;
        this.isAwake = false;
        
        // 状态管理
        this.state = 'sleeping'; // sleeping, listening, processing, responding
        this.lastWakeTime = 0;
        this.wakeTimeout = 20000; // 唤醒后10秒内等待命令

        // 错误处理
        this.errorCount = 0;
        this.maxErrors = 5;
        this.lastErrorTime = 0;

        // 调试模式
        this.debugMode = true; // 设置为true显示详细语音调试信息
        
        // 配置
        this.config = {
            language: 'zh-CN',
            continuous: true,
            interimResults: true,
            maxAlternatives: 3
        };
        
        // 唤醒词
        this.wakeWords = ['你好小飞', '小飞你好','你好，小飞'];
        
        // 命令词
        this.commands = {
            start: [ '开始讲解', '启动讲解'],
            pause: ['小飞暂停', '暂停讲解', '讲解暂停'],
            resume: ['小飞继续', '小飞恢复讲解'],
            stop: ['小飞结束', '结束讲解'],
            next: ['小飞跳过'],
            previous: ['小飞上一步']
        };
        
        // 语音应答
        this.responses = {
            wake: ['我在这里', '您好，我是小飞', '有什么可以帮您的吗？'],
            start: ['好的，开始为您讲解', '智能讲解模式已启动'],
            pause: ['讲解已暂停', '已为您暂停'],
            resume: ['继续为您讲解', '讲解已恢复'],
            stop: ['讲解已结束', '感谢您的使用'],
            next: ['好的，下一步', '为您切换到下一步'],
            previous: ['好的，上一步', '为您返回上一步'],
            error: ['抱歉，我没有听清楚', '请再说一遍'],
            timeout: ['我先休息一下，需要时请再次唤醒我']
        };
        
        // 绑定方法
        this.handleResult = this.handleResult.bind(this);
        this.handleError = this.handleError.bind(this);
        this.handleEnd = this.handleEnd.bind(this);
    }
    
    /**
     * 初始化语音控制器
     */
    async init() {
        try {
            // 检查浏览器支持
            if (!this.checkSupport()) {
                console.warn('当前浏览器不支持语音识别功能');
                return false;
            }
            
            // 创建语音识别实例
            this.createRecognition();
            
            // 请求麦克风权限
            await this.requestPermission();
            
            console.log('✅ 语音控制器初始化成功');
            return true;
        } catch (error) {
            console.error('❌ 语音控制器初始化失败:', error);
            return false;
        }
    }
    
    /**
     * 检查浏览器支持
     */
    checkSupport() {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.isSupported = !!SpeechRecognition;
        return this.isSupported;
    }
    
    /**
     * 创建语音识别实例
     */
    createRecognition() {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();
        
        // 配置语音识别
        this.recognition.lang = this.config.language;
        this.recognition.continuous = this.config.continuous;
        this.recognition.interimResults = this.config.interimResults;
        this.recognition.maxAlternatives = this.config.maxAlternatives;
        
        // 绑定事件 - 使用箭头函数确保this指向正确
        this.recognition.onresult = (event) => {
            console.log('🎤 [事件] onresult 被触发');
            this.handleResult(event);
        };

        this.recognition.onerror = (event) => {
            console.log('🎤 [事件] onerror 被触发:', event.error);
            this.handleError(event);
        };

        this.recognition.onend = () => {
            console.log('🎤 [事件] onend 被触发');
            this.handleEnd();
        };

        this.recognition.onstart = () => {
            this.isListening = true;
            this.errorCount = 0; // 重置错误计数
            console.log('🎤 [事件] onstart 被触发 - 语音识别已启动');
        };

        this.recognition.onspeechstart = () => {
            console.log('🗣️ [事件] onspeechstart - 检测到语音输入开始');
        };

        this.recognition.onspeechend = () => {
            console.log('🔇 [事件] onspeechend - 语音输入结束');
        };

        this.recognition.onsoundstart = () => {
            console.log('🔊 [事件] onsoundstart - 检测到声音');
        };

        this.recognition.onsoundend = () => {
            console.log('� [事件] onsoundend - 声音结束');
        };

        // 添加更多事件监听器
        this.recognition.onaudiostart = () => {
            console.log('🎵 [事件] onaudiostart - 音频捕获开始');
        };

        this.recognition.onaudioend = () => {
            console.log('🎵 [事件] onaudioend - 音频捕获结束');
        };

        this.recognition.onnomatch = () => {
            console.log('❓ [事件] onnomatch - 没有匹配的识别结果');
        };
    }
    
    /**
     * 请求麦克风权限
     */
    async requestPermission() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            stream.getTracks().forEach(track => track.stop()); // 立即停止，只是为了获取权限
            return true;
        } catch (error) {
            throw new Error('麦克风权限被拒绝');
        }
    }
    
    /**
     * 开始监听
     */
    startListening() {
        if (!this.isSupported || !this.recognition) {
            console.warn('语音识别不可用');
            return false;
        }

        // 如果已经在监听，直接返回成功
        if (this.isListening) {
            return true;
        }

        try {
            this.recognition.start();
            this.state = 'sleeping';
            console.log('🎤 开始监听语音唤醒...');
            return true;
        } catch (error) {
            // 如果是状态错误，说明可能已经在运行，不报错
            if (error.name === 'InvalidStateError') {
                console.warn('语音识别已在运行中');
                return true;
            }
            console.error('启动语音识别失败:', error);
            return false;
        }
    }
    
    /**
     * 停止监听
     */
    stopListening() {
        if (this.recognition && this.isListening) {
            this.recognition.stop();
            this.isListening = false;
            this.isAwake = false;
            this.state = 'sleeping';
            console.log('🔇 语音识别已停止');
        }
    }
    
    /**
     * 处理语音识别结果
     */
    handleResult(event) {
        console.log('🎤 [调试] handleResult 方法被调用');
        console.log('🎤 [调试] event.results 长度:', event.results.length);

        const results = event.results;

        // 显示所有结果
        for (let i = 0; i < results.length; i++) {
            const result = results[i];
            console.log(`🎤 [调试] 结果 ${i}:`, {
                isFinal: result.isFinal,
                length: result.length,
                transcript: result[0] ? result[0].transcript : 'undefined'
            });
        }

        const lastResult = results[results.length - 1];

        // 显示实时语音内容（包括临时结果）
        if (lastResult && lastResult[0]) {
            const transcript = lastResult[0].transcript.trim();
            const confidence = lastResult[0].confidence;

            console.log('🎤 [调试] 处理结果:', {
                transcript: transcript,
                confidence: confidence,
                isFinal: lastResult.isFinal,
                length: transcript.length
            });

            if (lastResult.isFinal) {
                // 最终结果总是显示，包括空结果
                if (transcript) {
                    console.log('🎤 [最终] 识别到语音:', `"${transcript}"`, confidence ? `(置信度: ${(confidence * 100).toFixed(1)}%)` : '');
                    console.log('🔍 [分析] 语音长度:', transcript.length, '字符');
                    this.processTranscript(transcript);
                } else {
                    console.log('🎤 [最终] 识别结果为空');
                }
            } else if (this.debugMode) {
                // 临时结果只在调试模式下显示
                if (transcript) {
                    console.log('🎤 [临时] 实时语音:', `"${transcript}"`);
                } else {
                    console.log('🎤 [临时] 正在识别...');
                }
            }
        } else {
            console.log('🎤 [警告] 没有有效的识别结果');
            console.log('🎤 [调试] lastResult:', lastResult);
        }
    }
    
    /**
     * 处理识别到的文本
     */
    async processTranscript(transcript) {
        const lowerTranscript = transcript.toLowerCase();

        console.log('🔍 [分析] 当前状态:', this.state, '是否已唤醒:', this.isAwake);
        console.log('🔍 [分析] 原始文本:', `"${transcript}"`);
        console.log('🔍 [分析] 小写文本:', `"${lowerTranscript}"`);

        // 检查唤醒词
        if (this.state === 'sleeping' && this.detectWakeWord(lowerTranscript)) {
            console.log('✅ [唤醒] 检测到唤醒词！');
            await this.handleWakeUp();
            return;
        } else if (this.state === 'sleeping') {
            console.log('❌ [唤醒] 未检测到唤醒词，继续等待...');
            console.log('🔍 [唤醒] 支持的唤醒词:', this.wakeWords);
        }

        // 如果已唤醒，处理命令
        if (this.isAwake) {
            console.log('🎯 [命令] 系统已唤醒，开始识别命令...');
            const command = this.detectCommand(lowerTranscript);
            if (command) {
                console.log('✅ [命令] 识别到命令:', command);
                await this.executeCommand(command);
            } else {
                console.log('❌ [命令] 未识别到有效命令');
                console.log('🔍 [命令] 支持的命令:', Object.keys(this.commands));

                // 检查是否正在讲解中，如果是则不要应答错误
                const isPresenting = this.isPresentationActive();
                if (!isPresenting) {
                    await this.respondWithError();
                } else {
                    console.log('🎯 [命令] 正在讲解中，忽略无效命令');
                    // 重置唤醒时间，保持监听状态
                    this.lastWakeTime = Date.now();
                }
            }
        }
    }
    
    /**
     * 检测唤醒词
     */
    detectWakeWord(transcript) {
        console.log('🔍 [唤醒检测] 开始检测唤醒词...');

        for (const word of this.wakeWords) {
            const lowerWord = word.toLowerCase();
            console.log(`🔍 [唤醒检测] 检查唤醒词: "${lowerWord}"`);

            if (transcript.includes(lowerWord)) {
                console.log(`✅ [唤醒检测] 匹配成功: "${lowerWord}"`);
                return true;
            }
        }

        console.log('❌ [唤醒检测] 没有匹配的唤醒词');
        return false;
    }
    
    /**
     * 检测命令词
     */
    detectCommand(transcript) {
        console.log('🔍 [命令检测] 开始检测命令词...');

        for (const [command, keywords] of Object.entries(this.commands)) {
            console.log(`🔍 [命令检测] 检查命令: ${command}`);

            for (const keyword of keywords) {
                const lowerKeyword = keyword.toLowerCase();
                console.log(`🔍 [命令检测] 检查关键词: "${lowerKeyword}"`);

                if (transcript.includes(lowerKeyword)) {
                    console.log(`✅ [命令检测] 匹配成功: ${command} -> "${lowerKeyword}"`);
                    return command;
                }
            }
        }

        console.log('❌ [命令检测] 没有匹配的命令');
        return null;
    }
    
    /**
     * 处理唤醒
     */
    async handleWakeUp() {
        this.isAwake = true;
        this.state = 'listening';
        this.lastWakeTime = Date.now();
        
        console.log('👋 语音唤醒成功');
        
        // 语音应答
        await this.respond('wake');
        
        // 设置超时，如果一段时间内没有命令则进入休眠
        setTimeout(() => {
            if (this.isAwake && Date.now() - this.lastWakeTime >= this.wakeTimeout) {
                this.handleTimeout();
            }
        }, this.wakeTimeout);
    }
    
    /**
     * 执行命令
     */
    async executeCommand(command) {
        this.state = 'processing';
        console.log('🎯 [命令] 开始执行命令:', command);

        try {
            // 重置唤醒时间，防止在命令执行过程中超时
            this.lastWakeTime = Date.now();

            switch (command) {
                case 'start':
                    // 只有在未讲解时才执行启动
                    if (!this.isPresentationActive()) {
                        await this.startPresentation();
                        // 启动讲解后不需要额外应答，让讲解内容自然开始
                        return; // 不执行语音应答
                    } else {
                        console.log('⚠️ [命令] 讲解已在进行中，忽略启动命令');
                        return; // 不执行语音应答
                    }
                case 'pause':
                    // 只有在讲解中才能暂停
                    if (this.isPresentationActive()) {
                        await this.pausePresentation();
                    } else {
                        console.log('⚠️ [命令] 当前没有讲解在进行，无法暂停');
                        return;
                    }
                    break;
                case 'resume':
                    await this.resumePresentation();
                    break;
                case 'stop':
                    await this.stopPresentation();
                    break;
                case 'next':
                    // 只有在讲解中才能跳转
                    if (this.isPresentationActive()) {
                        await this.nextStep();
                    } else {
                        console.log('⚠️ [命令] 当前没有讲解在进行，无法跳转');
                        return;
                    }
                    break;
                case 'previous':
                    // 只有在讲解中才能跳转
                    if (this.isPresentationActive()) {
                        await this.previousStep();
                    } else {
                        console.log('⚠️ [命令] 当前没有讲解在进行，无法跳转');
                        return;
                    }
                    break;
                default:
                    console.error('❌ [命令] 未知命令:', command);
                    await this.respondWithError();
                    return;
            }

            // 命令执行成功后应答
            console.log('✅ [命令] 命令执行成功，准备语音应答');
            await this.respond(command);

        } catch (error) {
            console.error('❌ [命令] 命令执行失败:', error);
            await this.respondWithError();
        }

        // 如果不是停止命令，保持监听状态
        if (command !== 'stop') {
            this.state = 'listening';
        }

        console.log('🎯 [命令] 命令执行完成，当前状态:', this.state);
    }
    
    /**
     * 语音应答
     */
    async respond(type) {
        if (!this.responses[type]) return;
        
        this.state = 'responding';
        
        const responses = this.responses[type];
        const response = responses[Math.floor(Math.random() * responses.length)];
        
        console.log('🗣️ 语音应答:', response);
        
        // 使用现有的语音引擎进行应答
        if (window.presentationController && window.presentationController.speechEngine) {
            try {
                await window.presentationController.speechEngine.speak(response);
            } catch (error) {
                console.warn('语音应答失败:', error);
            }
        }
        
        this.state = 'listening';
    }
    
    /**
     * 错误应答
     */
    async respondWithError() {
        await this.respond('error');
    }

    /**
     * 检查讲解系统是否处于活动状态
     */
    isPresentationActive() {
        try {
            if (!window.presentationController) {
                return false;
            }

            // 检查是否有isPlaying属性或方法
            if (typeof window.presentationController.isPlaying === 'function') {
                return window.presentationController.isPlaying();
            } else if (typeof window.presentationController.isPlaying === 'boolean') {
                return window.presentationController.isPlaying;
            }

            // 备用检查：检查是否有currentStep和totalSteps
            const hasSteps = window.presentationController.currentStep !== undefined &&
                           window.presentationController.totalSteps !== undefined;

            if (hasSteps) {
                return window.presentationController.currentStep > 0 &&
                       window.presentationController.currentStep <= window.presentationController.totalSteps;
            }

            return false;
        } catch (error) {
            console.warn('检查讲解状态时出错:', error);
            return false;
        }
    }
    
    /**
     * 处理超时
     */
    async handleTimeout() {
        // 检查是否正在讲解中，如果是则不进入休眠
        const isPresenting = this.isPresentationActive();

        if (isPresenting) {
            console.log('⏰ 检测到超时，但正在讲解中，延长唤醒时间');
            this.lastWakeTime = Date.now(); // 重置唤醒时间

            // 重新设置超时检查
            setTimeout(() => {
                if (this.isAwake && Date.now() - this.lastWakeTime >= this.wakeTimeout) {
                    this.handleTimeout();
                }
            }, this.wakeTimeout);
        } else {
            console.log('⏰ 语音交互超时，进入休眠');
            this.isAwake = false;
            this.state = 'sleeping';
            await this.respond('timeout');
        }
    }
    
    /**
     * 处理语音识别错误
     */
    handleError(event) {
        const now = Date.now();

        // 过滤掉常见的非关键错误，避免控制台噪音
        const ignoredErrors = ['no-speech', 'network', 'aborted', 'not-allowed'];

        if (!ignoredErrors.includes(event.error)) {
            console.error('语音识别错误:', event.error);
        }

        // 错误计数和限制
        if (now - this.lastErrorTime < 5000) {
            this.errorCount++;
        } else {
            this.errorCount = 1;
        }
        this.lastErrorTime = now;

        // 如果错误过多，暂停重试
        if (this.errorCount >= this.maxErrors) {
            console.warn('语音识别错误过多，暂停重试');
            return;
        }

        // 如果是网络错误或其他可恢复错误，尝试重启
        const recoverableErrors = ['network', 'audio-capture', 'no-speech', 'aborted'];
        if (recoverableErrors.includes(event.error)) {
            const delay = Math.min(1000 * this.errorCount, 5000); // 递增延迟，最大5秒
            setTimeout(() => {
                if (!this.isListening && this.state !== 'sleeping') {
                    this.startListening();
                }
            }, delay);
        }
    }
    
    /**
     * 处理语音识别结束
     */
    handleEnd() {
        this.isListening = false;

        // 如果不是主动停止，则自动重启监听（添加延迟避免过于频繁）
        if (this.state !== 'sleeping') {
            setTimeout(() => {
                if (!this.isListening) {
                    this.startListening();
                }
            }, 500); // 增加延迟到500ms
        }
    }
    
    // ===== 智能讲解系统集成方法 =====
    
    /**
     * 启动智能讲解
     */
    async startPresentation() {
        try {
            // 检查是否已经在讲解中
            if (this.isPresentationActive()) {
                console.log('⚠️ [命令执行] 智能讲解已在进行中，忽略启动命令');
                return;
            }

            if (window.PresentationSystem && window.PresentationSystem.start) {
                console.log('🎯 [命令执行] 启动智能讲解系统...');
                await window.PresentationSystem.start();
                console.log('✅ [命令执行] 智能讲解系统启动成功');
            } else {
                console.error('❌ [命令执行] 智能讲解系统不可用');
                throw new Error('智能讲解系统不可用');
            }
        } catch (error) {
            console.error('❌ [命令执行] 启动智能讲解失败:', error);
            throw error;
        }
    }
    
    /**
     * 暂停讲解
     */
    async pausePresentation() {
        try {
            if (window.presentationController && window.presentationController.pause) {
                console.log('🎯 [命令执行] 暂停智能讲解...');
                window.presentationController.pause();
                console.log('✅ [命令执行] 智能讲解已暂停');

                // 暂停后保持唤醒状态，不要进入休眠
                this.lastWakeTime = Date.now();
            } else {
                console.error('❌ [命令执行] 讲解系统不可用');
                throw new Error('讲解系统不可用');
            }
        } catch (error) {
            console.error('❌ [命令执行] 暂停讲解失败:', error);
            throw error;
        }
    }

    /**
     * 恢复讲解
     */
    async resumePresentation() {
        try {
            if (window.presentationController && window.presentationController.resume) {
                console.log('🎯 [命令执行] 恢复智能讲解...');
                window.presentationController.resume();
                console.log('✅ [命令执行] 智能讲解已恢复');

                // 恢复后重置唤醒时间
                this.lastWakeTime = Date.now();
            } else {
                console.error('❌ [命令执行] 讲解系统不可用');
                throw new Error('讲解系统不可用');
            }
        } catch (error) {
            console.error('❌ [命令执行] 恢复讲解失败:', error);
            throw error;
        }
    }
    
    /**
     * 停止讲解
     */
    async stopPresentation() {
        try {
            if (window.presentationController && window.presentationController.stop) {
                console.log('🎯 [命令执行] 停止智能讲解...');
                window.presentationController.stop();
                console.log('✅ [命令执行] 智能讲解已停止');

                // 停止后可以进入休眠
                this.isAwake = false;
                this.state = 'sleeping';
            } else {
                console.error('❌ [命令执行] 讲解系统不可用');
                throw new Error('讲解系统不可用');
            }
        } catch (error) {
            console.error('❌ [命令执行] 停止讲解失败:', error);
            throw error;
        }
    }
    
    /**
     * 下一步
     */
    async nextStep() {
        try {
            if (window.presentationController && window.presentationController.next) {
                console.log('🎯 [命令执行] 跳转到下一步...');
                await window.presentationController.next();
                console.log('✅ [命令执行] 已跳转到下一步');

                // 操作后重置唤醒时间
                this.lastWakeTime = Date.now();
            } else {
                console.error('❌ [命令执行] 讲解系统不可用');
                throw new Error('讲解系统不可用');
            }
        } catch (error) {
            console.error('❌ [命令执行] 跳转下一步失败:', error);
            throw error;
        }
    }

    /**
     * 上一步
     */
    async previousStep() {
        try {
            if (window.presentationController && window.presentationController.previous) {
                console.log('🎯 [命令执行] 跳转到上一步...');
                await window.presentationController.previous();
                console.log('✅ [命令执行] 已跳转到上一步');

                // 操作后重置唤醒时间
                this.lastWakeTime = Date.now();
            } else {
                console.error('❌ [命令执行] 讲解系统不可用');
                throw new Error('讲解系统不可用');
            }
        } catch (error) {
            console.error('❌ [命令执行] 跳转上一步失败:', error);
            throw error;
        }
    }
    
    /**
     * 获取当前状态
     */
    getStatus() {
        let presentationStatus = null;

        try {
            if (window.presentationController) {
                presentationStatus = {
                    isPlaying: this.isPresentationActive(),
                    currentStep: window.presentationController.currentStep || 0,
                    totalSteps: window.presentationController.totalSteps || 0
                };
            }
        } catch (error) {
            console.warn('获取讲解状态时出错:', error);
        }

        return {
            isSupported: this.isSupported,
            isListening: this.isListening,
            isAwake: this.isAwake,
            state: this.state,
            debugMode: this.debugMode,
            presentation: presentationStatus
        };
    }

    /**
     * 设置调试模式
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`🔧 语音调试模式已${enabled ? '开启' : '关闭'}`);
    }

    /**
     * 诊断语音识别状态
     */
    diagnose() {
        console.log('📊 ===== 语音识别诊断报告 =====');
        console.log('📊 浏览器支持:', this.isSupported ? '✅ 支持' : '❌ 不支持');
        console.log('📊 识别实例:', this.recognition ? '✅ 已创建' : '❌ 未创建');
        console.log('📊 当前状态:', this.state);
        console.log('📊 是否监听:', this.isListening ? '✅ 正在监听' : '❌ 未监听');
        console.log('📊 是否唤醒:', this.isAwake ? '✅ 已唤醒' : '❌ 未唤醒');
        console.log('📊 调试模式:', this.debugMode ? '✅ 已开启' : '❌ 未开启');
        console.log('📊 错误计数:', this.errorCount);
        console.log('📊 唤醒词:', this.wakeWords);
        console.log('📊 支持的命令:');

        for (const [command, keywords] of Object.entries(this.commands)) {
            console.log(`📊   - ${command}: ${keywords.join(', ')}`);
        }

        console.log('📊 ===== 诊断报告结束 =====');

        // 检查麦克风权限
        navigator.mediaDevices.getUserMedia({ audio: true })
            .then(() => console.log('📊 麦克风权限: ✅ 已授权'))
            .catch(err => console.log('📊 麦克风权限: ❌ 未授权', err));

        return '诊断完成，请查看控制台输出';
    }

    /**
     * 简化测试模式 - 用最基本的配置测试语音识别
     */
    async testBasicRecognition() {
        console.log('🧪 ===== 开始基础语音识别测试 =====');

        try {
            // 停止当前识别
            if (this.isListening) {
                this.recognition.stop();
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // 创建一个简化的测试识别器
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            const testRecognition = new SpeechRecognition();

            // 最基本的配置
            testRecognition.lang = 'zh-CN';
            testRecognition.continuous = false;  // 简化：不连续
            testRecognition.interimResults = false;  // 简化：只要最终结果
            testRecognition.maxAlternatives = 1;

            // 绑定事件
            testRecognition.onstart = () => {
                console.log('🧪 [测试] 语音识别已启动，请说话...');
            };

            testRecognition.onresult = (event) => {
                console.log('🧪 [测试] 收到识别结果！');
                const result = event.results[0][0];
                console.log('🧪 [测试] 识别内容:', `"${result.transcript}"`);
                console.log('🧪 [测试] 置信度:', (result.confidence * 100).toFixed(1) + '%');
            };

            testRecognition.onerror = (event) => {
                console.log('🧪 [测试] 识别错误:', event.error);
            };

            testRecognition.onend = () => {
                console.log('🧪 [测试] 识别结束');
                console.log('🧪 ===== 基础测试完成 =====');

                // 重启原来的识别器
                setTimeout(() => {
                    this.startListening();
                }, 1000);
            };

            // 启动测试
            testRecognition.start();

            return '基础测试已启动，请说话...';

        } catch (error) {
            console.error('🧪 [测试] 测试失败:', error);
            return '测试失败: ' + error.message;
        }
    }
}

// 导出到全局
window.VoiceController = VoiceController;
