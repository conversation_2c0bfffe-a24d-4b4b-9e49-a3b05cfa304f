/**
 * 语音管理器 - 管理语音控制器的生命周期和UI交互
 */
class VoiceManager {
    constructor() {
        this.voiceController = null;
        this.isInitialized = false;
        this.isEnabled = false;
        
        // UI元素
        this.toggleButton = null;
        this.statusIndicator = null;
        
        // 绑定方法
        this.handleToggle = this.handleToggle.bind(this);
    }
    
    /**
     * 初始化语音管理器
     */
    async init() {
        try {
            // 创建语音控制器
            this.voiceController = new window.VoiceController();
            
            // 初始化语音控制器
            const success = await this.voiceController.init();
            
            if (success) {
                this.isInitialized = true;
                
                // 创建UI控件
                this.createUI();
                
                console.log('✅ 语音管理器初始化成功');
                return true;
            } else {
                console.warn('⚠️ 语音控制器初始化失败');
                return false;
            }
        } catch (error) {
            console.error('❌ 语音管理器初始化失败:', error);
            return false;
        }
    }
    
    /**
     * 创建UI控件
     */
    createUI() {
        // 检测运行环境
        const isInMainPage = window.self === window.top;

        if (isInMainPage) {
            console.log('🎤 主页面环境：语音控制已准备就绪（默认关闭）');
            // 在主页面中，添加快捷键但不默认启用
            this.addKeyboardShortcuts();

            // 语音控制默认关闭，等待用户主动触发
            // 可以通过 Ctrl+空格 快捷键或调用 this.enable() 来启用
        } else {
            console.log('🎤 iframe环境：跳过语音控制初始化');
            // 在iframe中，不初始化语音控制
        }
    }
    
    /**
     * 添加键盘快捷键
     */
    addKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl + 空格 切换语音控制
            // 使用多种检测方式确保兼容性
            const isCtrlSpace = event.ctrlKey &&
                               (event.key === ' ' ||
                                event.code === 'Space' ||
                                event.keyCode === 32);

            if (isCtrlSpace) {
                event.preventDefault();
                event.stopPropagation();
                event.stopImmediatePropagation();

                console.log('🎹 检测到Ctrl+空格快捷键');
                console.log('🎹 事件详情:', {
                    key: event.key,
                    code: event.code,
                    keyCode: event.keyCode,
                    ctrlKey: event.ctrlKey,
                    browser: navigator.userAgent.includes('Edg') ? 'Edge' : 'Other'
                });

                this.handleToggle();

                const status = this.isEnabled ? '启用' : '禁用';
                console.log(`🎹 快捷键: ${status}语音控制 (Ctrl+空格)`);
            }
        }, { capture: true }); // 使用捕获阶段确保优先处理

        // 为Edge添加额外的兼容性修复
        if (navigator.userAgent.includes('Edg')) {
            this.addEdgeCompatibilityFix();
        }

        console.log('🎹 语音控制快捷键已启用:');
        console.log('🎹   Ctrl+空格: 启用/禁用语音控制（当前：关闭）');
        console.log('🎹   浏览器:', navigator.userAgent.includes('Edg') ? 'Edge' : 'Other');
    }

    /**
     * Edge浏览器兼容性修复
     */
    addEdgeCompatibilityFix() {
        console.log('🔧 启用Edge浏览器兼容性修复 (voice-manager)');

        // 添加keyup事件监听
        document.addEventListener('keyup', (event) => {
            const isCtrlSpace = event.ctrlKey &&
                               (event.key === ' ' ||
                                event.code === 'Space' ||
                                event.keyCode === 32);

            if (isCtrlSpace) {
                console.log('🔧 Edge兼容性: 在keyup中检测到Ctrl+空格 (voice-manager)');
                event.preventDefault();
                event.stopPropagation();
                this.handleToggle();

                const status = this.isEnabled ? '启用' : '禁用';
                console.log(`🎹 快捷键: ${status}语音控制 (Ctrl+空格) [Edge兼容模式]`);
            }
        }, { capture: true });

        // 添加window级别监听器
        window.addEventListener('keydown', (event) => {
            const isCtrlSpace = event.ctrlKey &&
                               (event.key === ' ' ||
                                event.code === 'Space' ||
                                event.keyCode === 32);

            if (isCtrlSpace) {
                console.log('🔧 Edge兼容性: 在window级别检测到Ctrl+空格 (voice-manager)');
                event.preventDefault();
                event.stopPropagation();
                this.handleToggle();

                const status = this.isEnabled ? '启用' : '禁用';
                console.log(`🎹 快捷键: ${status}语音控制 (Ctrl+空格) [Edge Window级别]`);
            }
        }, { capture: true });

        console.log('🔧 Edge兼容性修复已启用 (voice-manager)');
    }
    
    /**
     * 创建状态指示器
     */
    createStatusIndicator() {
        // 检查是否已存在
        if (document.getElementById('voice-status-indicator')) {
            return;
        }
        
        const indicator = document.createElement('div');
        indicator.id = 'voice-status-indicator';
        indicator.className = 'voice-status-indicator hidden';
        indicator.innerHTML = `
            <div class="voice-status-content">
                <i class="fas fa-microphone"></i>
                <span class="voice-status-text">等待唤醒...</span>
                <div class="voice-wave-animation">
                    <div class="wave"></div>
                    <div class="wave"></div>
                    <div class="wave"></div>
                </div>
            </div>
        `;
        
        // 添加样式
        this.addStatusIndicatorStyles();
        
        // 添加到页面
        document.body.appendChild(indicator);
        
        this.statusIndicator = indicator;
    }
    

    
    /**
     * 添加状态指示器样式
     */
    addStatusIndicatorStyles() {
        const style = document.createElement('style');
        style.textContent = `
            .voice-status-indicator {
                position: fixed;
                top: 80px;
                left: 20px;
                z-index: 9999;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                border-radius: 15px;
                padding: 15px 20px;
                font-size: 14px;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                transition: all 0.3s ease;
                max-width: 250px;
            }
            
            .voice-status-indicator.hidden {
                opacity: 0;
                transform: translateX(-100%);
                pointer-events: none;
            }
            
            .voice-status-content {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            
            .voice-status-indicator i {
                font-size: 16px;
                color: #4CAF50;
            }
            
            .voice-status-indicator.listening i {
                color: #2196F3;
                animation: pulse 1.5s infinite;
            }
            
            .voice-status-indicator.awake i {
                color: #FF9800;
                animation: pulse 1s infinite;
            }
            
            .voice-status-indicator.processing i {
                color: #9C27B0;
                animation: spin 1s linear infinite;
            }
            
            .voice-wave-animation {
                display: none;
                gap: 2px;
            }
            
            .voice-status-indicator.listening .voice-wave-animation {
                display: flex;
            }
            
            .wave {
                width: 3px;
                height: 15px;
                background: #2196F3;
                border-radius: 2px;
                animation: wave 1.5s infinite ease-in-out;
            }
            
            .wave:nth-child(2) {
                animation-delay: 0.1s;
            }
            
            .wave:nth-child(3) {
                animation-delay: 0.2s;
            }
            
            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.5; }
            }
            
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            
            @keyframes wave {
                0%, 40%, 100% { transform: scaleY(0.4); }
                20% { transform: scaleY(1); }
            }
        `;
        document.head.appendChild(style);
    }
    
    /**
     * 处理开关切换
     */
    async handleToggle() {
        if (!this.isInitialized) {
            console.warn('语音控制器未初始化');
            return;
        }
        
        if (this.isEnabled) {
            this.disable();
        } else {
            await this.enable();
        }
    }
    
    /**
     * 启用语音控制
     */
    async enable() {
        try {
            const success = this.voiceController.startListening();
            
            if (success) {
                this.isEnabled = true;
                this.updateUI();
                this.startStatusMonitoring();
                console.log('✅ 语音控制已启用');
            } else {
                console.error('❌ 启用语音控制失败');
            }
        } catch (error) {
            console.error('❌ 启用语音控制时出错:', error);
        }
    }
    
    /**
     * 禁用语音控制
     */
    disable() {
        this.voiceController.stopListening();
        this.isEnabled = false;
        this.updateUI();
        this.stopStatusMonitoring();
        console.log('🔇 语音控制已禁用');
    }
    
    /**
     * 更新UI状态
     */
    updateUI() {
        // 不再显示任何UI元素
    }
    
    /**
     * 开始状态监控
     */
    startStatusMonitoring() {
        this.statusMonitorInterval = setInterval(() => {
            this.updateStatusIndicator();
        }, 500);
    }
    
    /**
     * 停止状态监控
     */
    stopStatusMonitoring() {
        if (this.statusMonitorInterval) {
            clearInterval(this.statusMonitorInterval);
            this.statusMonitorInterval = null;
        }
    }
    
    /**
     * 更新状态指示器
     */
    updateStatusIndicator() {
        if (!this.statusIndicator || !this.voiceController) return;
        
        const status = this.voiceController.getStatus();
        const textElement = this.statusIndicator.querySelector('.voice-status-text');
        
        // 移除所有状态类
        this.statusIndicator.classList.remove('listening', 'awake', 'processing');
        
        // 根据状态更新UI
        switch (status.state) {
            case 'sleeping':
                textElement.textContent = '等待唤醒...';
                break;
            case 'listening':
                textElement.textContent = status.isAwake ? '等待命令...' : '监听中...';
                this.statusIndicator.classList.add(status.isAwake ? 'awake' : 'listening');
                break;
            case 'processing':
                textElement.textContent = '处理命令中...';
                this.statusIndicator.classList.add('processing');
                break;
            case 'responding':
                textElement.textContent = '语音应答中...';
                this.statusIndicator.classList.add('processing');
                break;
        }
    }
    
    /**
     * 获取状态
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            isEnabled: this.isEnabled,
            voiceController: this.voiceController ? this.voiceController.getStatus() : null
        };
    }
}

// 导出到全局
window.VoiceManager = VoiceManager;

// 全局调试控制函数
window.setVoiceDebug = function(enabled) {
    if (window.voiceManager && window.voiceManager.voiceController) {
        window.voiceManager.voiceController.setDebugMode(enabled);
    } else {
        console.warn('语音控制系统未初始化');
    }
};

// 全局诊断函数
window.diagnoseVoice = function() {
    if (window.voiceManager && window.voiceManager.voiceController) {
        return window.voiceManager.voiceController.diagnose();
    } else {
        console.warn('语音控制系统未初始化');
        return '语音控制系统未初始化';
    }
};

// 全局测试函数
window.testVoiceRecognition = function() {
    console.log('🧪 开始语音识别测试...');
    console.log('🧪 请在接下来的10秒内说话，系统会显示识别结果');

    if (window.voiceManager && window.voiceManager.voiceController) {
        const controller = window.voiceManager.voiceController;
        const originalDebugMode = controller.debugMode;

        // 临时开启调试模式
        controller.setDebugMode(true);

        // 10秒后恢复原始调试模式
        setTimeout(() => {
            controller.setDebugMode(originalDebugMode);
            console.log('🧪 语音识别测试结束');
        }, 10000);

        return '测试已开始，请说话...';
    } else {
        return '语音控制系统未初始化';
    }
};

// 基础语音识别测试
window.testBasicVoice = function() {
    if (window.voiceManager && window.voiceManager.voiceController) {
        return window.voiceManager.voiceController.testBasicRecognition();
    } else {
        return '语音控制系统未初始化';
    }
};
